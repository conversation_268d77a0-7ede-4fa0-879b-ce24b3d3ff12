# SonarQube Analysis Reports

This directory contains SonarQube analysis reports for the Healtether Clinics API project.

## How to Run SonarQube Analysis

### Prerequisites
- SonarQube server running on `http://localhost:9000`
- SonarQube scanner installed
- Authentication token configured in `sonar-project.properties`

### Running Analysis

1. **Run SonarQube Scan:**
   ```bash
   npm run sonar
   ```

2. **Generate Test Coverage (optional but recommended):**
   ```bash
   npm run test:coverage
   ```

3. **Fetch Detailed API Results:**
   ```bash
   node ../sonarqube-api-client.js healtether-clinics-api
   ```

### Viewing Results

- **SonarQube Dashboard:** http://localhost:9000/dashboard?id=healtether-clinics-api
- **Detailed JSON Reports:** Check files in this directory after running the API client

### Project Configuration

- **Project Key:** `healtether-clinics-api`
- **Authentication Token:** `sqp_9833dac32acc6233060116e797a16d7c314fcf5c`
- **Quality Gate Requirements:**
  - Test Coverage: ≥ 80%
  - Duplicated Lines: < 3%
  - New Violations: 0

### Files Generated
- `sonarqube-report-healtether-clinics-api.json` - Detailed analysis results
- Additional reports as generated by the analysis tools
