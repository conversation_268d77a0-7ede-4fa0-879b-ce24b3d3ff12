{"name": "sonarjs", "version": "1.0.0", "description": "SonarJS code analyzer", "scripts": {"format": "prettier --write .", "check-format": "prettier --list-different .", "build": "mvn clean && npm run bridge:build && npm run _:plugin:pre-build && npm run plugin:build", "build:cov": "mvn clean && npm run bridge:build:cov && npm run _:plugin:pre-build && npm run plugin:build", "build:fast": "npm run bridge:build:fast && npm run _:plugin:pre-build && npm run plugin:build:fast", "bf": "npm run build:fast", "new-rule": "tsx tools/new-rule.mts", "generate-meta": "tsx tools/generate-meta.ts npm run eslint-docs", "generate-java-rule-classes": "tsx tools/generate-java-rule-classes.ts", "ruling": "tsx --tsconfig packages/tsconfig.test.json --test packages/ruling/tests/projects/*.ruling.test.ts", "ruling-parametrized": "SONAR_RULING_SETTINGS=../settings.js  tsx --tsconfig packages/tsconfig.test.json --test packages/ruling/tests/projects/*.ruling.test.ts", "ruling-sync": "rsync -avh packages/ruling/tests/actual/jsts/ its/ruling/src/test/expected/jsts/ --delete", "create-rule-indexes": "tsx tools/generate-rule-indexes.ts", "bridge:compile": "tsc -b packages && npm run _:bridge:copy-protofiles", "bridge:test": "tsx --tsconfig packages/tsconfig.test.json --test --test-concurrency=4 --test-reporter spec --test-reporter-destination stdout \"packages/*/src/rules/*[!node_modules]/**/*.test.ts\" \"packages/*[!ruling]/tests/**/*.test.ts\"", "bridge:test:js": "tsx tools/copy-test-resources.ts && node --test --test-concurrency=4 --test-reporter spec --test-reporter-destination stdout \"lib/*/src/rules/*[!node_modules]/**/*.test.js\" \"lib/*[!ruling]/tests/**/*.test.js\"", "bridge:test:cov": "cross-env 'NODE_OPTIONS=--import ./tools/nyc-esm-hook-loader.js' nyc npm run bridge:test:js", "bridge:bundle": "node esbuild.mjs", "bridge:build": "npm run bridge:build:fast && npm run bridge:test", "bridge:build:cov": "npm run bridge:build:fast && npm run bridge:test:cov", "bridge:build:fast": "npm run _:bridge:clear && npm run generate-meta && npm run bridge:compile", "bbf": "npm run bridge:build:fast", "plugin:build": "mvn install", "plugin:build:fast": "mvn install -DskipTests", "pbf": "npm run plugin:build:fast", "td": "npm run generate-meta && npm --prefix typedoc/searchable-parameters-plugin run setup && npx typedoc --options typedoc/typedoc.js", "prepare": "husky", "precommit": "pretty-quick --staged", "postinstall": "patch-package", "count-rules": "tsx tools/count-rules.ts", "_:bridge:copy-protofiles": "cpy --flat packages/jsts/src/parsers/estree.proto lib/jsts/src/parsers", "_:bridge:clear": "rimraf --glob lib/*", "_:plugin:prepare-bridge": "npm run bridge:bundle && npm pack --ignore-scripts && npm run _:plugin:copy-bridge", "_:plugin:pre-build": "npm run _:plugin:prepare-bridge", "_:plugin:copy-bridge": "cpy sonarjs-1.0.0.tgz sonar-plugin/sonar-javascript-plugin/target/classes && cpy sonarjs-1.0.0.tgz sonar-plugin/standalone/target/classes", "eslint-plugin:build": "npm ci && npm run generate-meta && npm run eslint-plugin:compile && cd lib && npm pack", "eslint-plugin:compile": "npm run _:bridge:clear && npm run eslint-plugin:check && npm run eslint-plugin:emit && npm run eslint-plugin:types && npm run eslint-plugin:package-json && npm run eslint-plugin:copy-assets", "eslint-plugin:check": "tsc -p tsconfig-plugin.json --noEmit", "eslint-plugin:emit": "tsc -p tsconfig-plugin.json --noCheck --module commonjs --moduleResolution node --outDir lib/cjs", "eslint-plugin:types": "tsc -p tsconfig-plugin.json --declaration true --emitDeclarationOnly --outDir lib/types", "eslint-plugin:package-json": "node generate-eslint-package-json.mjs", "eslint-plugin:copy-assets": "cpy LICENSE.txt lib/ --rename LICENSE && cpy packages/jsts/src/rules/README.md lib/ --flat", "eslint-docs": "npm run eslint-plugin:compile && eslint-doc-generator lib --init-rule-docs && tsx tools/generate-external-rules-docs.ts", "deploy-rule-data": "tsx tools/deploy-rule-data.ts"}, "repository": {"type": "git", "url": "git+https://github.com/SonarSource/SonarJS.git"}, "license": "LGPL-3.0-only", "bugs": {"url": "https://community.sonarsource.com/"}, "homepage": "https://github.com/SonarSource/SonarJS#readme", "engines": {"node": ">=22"}, "type": "module", "devDependencies": {"@babel/preset-typescript": "7.27.1", "@inquirer/prompts": "7.5.1", "@istanbuljs/esm-loader-hook": "0.3.0", "@types/babel__preset-env": "7.10.0", "@types/bytes": "3.1.5", "@types/estree": "1.0.7", "@types/express": "5.0.1", "@types/functional-red-black-tree": "1.0.6", "@types/node": "22.15.17", "@types/semver": "7.7.0", "@types/tmp": "0.2.6", "cpy-cli": "5.0.0", "cross-env": "7.0.3", "dir-compare": "5.0.0", "esbuild": "0.25.4", "esbuild-plugin-copy": "2.1.1", "esbuild-plugin-text-replace": "1.3.0", "eslint-doc-generator": "2.1.2", "esprima": "4.0.1", "expect": "29.7.0", "extract-zip": "2.0.1", "fs-extra": "11.3.0", "glob": "11.0.2", "husky": "9.1.7", "json-schema-to-ts": "3.1.1", "memfs": "4.17.1", "mkdirp": "3.0.1", "node-fetch": "3.3.2", "nyc": "17.1.0", "prettier": "3.5.3", "prettier-plugin-java": "2.6.7", "pretty-quick": "4.1.1", "rimraf": "6.0.1", "tsx": "4.19.4", "type-fest": "4.41.0", "typedoc": "0.28.4"}, "dependencies": {"@babel/core": "7.27.1", "@babel/eslint-parser": "7.27.1", "@babel/plugin-proposal-decorators": "7.27.1", "@babel/preset-env": "7.27.2", "@babel/preset-flow": "7.27.1", "@babel/preset-react": "7.27.1", "@eslint-community/regexpp": "4.12.1", "@protobufjs/base64": "1.1.2", "@stylistic/eslint-plugin-ts": "4.2.0", "@types/lodash.merge": "4.6.9", "@typescript-eslint/eslint-plugin": "8.32.0", "@typescript-eslint/parser": "8.32.0", "@typescript-eslint/utils": "8.32.0", "builtin-modules": "3.3.0", "bytes": "3.1.2", "eslint": "9.26.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "express": "5.1.0", "functional-red-black-tree": "1.0.1", "globals": "16.0.0", "htmlparser2": "10.0.0", "http-status-codes": "^2.3.0", "jsx-ast-utils": "3.3.5", "lodash.merge": "4.6.2", "minimatch": "9.0.5", "module-alias": "2.2.3", "patch-package": "6.5.1", "postcss": "8.5.3", "postcss-html": "0.36.0", "postcss-less": "6.0.0", "postcss-sass": "0.5.0", "postcss-scss": "4.0.9", "postcss-syntax": "0.36.2", "postcss-value-parser": "4.2.0", "protobufjs": "7.5.1", "scslre": "0.3.0", "semver": "7.7.1", "stylelint": "15.11.0", "tar": "7.4.3", "tmp": "0.2.3", "typescript": "5.8.3", "vue-eslint-parser": "10.1.3", "yaml": "2.7.1"}, "prettier": {"printWidth": 100, "trailingComma": "all", "singleQuote": true, "arrowParens": "avoid", "endOfLine": "lf", "plugins": ["prettier-plugin-java"]}, "files": ["bin/"]}