const express= require('express');
const appRoute=require("./routes/route.js");

const app=express();

// Disable Express.js version disclosure for security
app.disable('x-powered-by');


//config
if (process.env.NODE_ENV !== "PRODUCTION") {
  require("dotenv").config({ path: "./data.env" });

}


const PORT=process.env.PORT||5000;

app.use(express.json());



//route
app.use("/api/v1",appRoute);

app.listen(PORT,()=>{
    console.log("server has started in post:5000")
})