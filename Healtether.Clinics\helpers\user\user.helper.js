import {ClientUser, User} from "../../model/clinics.model.js";
import bcrypt from "bcrypt";
import {findIndexInArray} from "../../utils/array.utils.js";
import crypto from "crypto";

// export const modelSubmission = async (data) => {     let { phone, email,
// password } = data;     const encrypt = await HashPassword(password); const
// authCollection = new User({       email,       phone,       password:
// encrypt,     });     await authCollection.save();     return authCollection;
// };
export const CreateOrUpdateByMobile = async(data) => {
    let {mobile, email, isAdmin, firstName, lastName} = data;
    const user = await User.findOne({mobile: mobile})
    if (user != null) {
        let _id = user._id
        return await User.findByIdAndUpdate(_id, {
            $set: {
                mobile: mobile,
                email: email,
                isAdmin: isAdmin,
                firstName: firstName,
                lastName: lastName
            }
        });
    } else {
        const encrypt = await hashPassword(generateId(8));
        const authCollection = new User({
            email: email,
            mobile: mobile,
            password: encrypt,
            isAdmin: isAdmin,
            firstName: firstName,
            lastName: lastName
        });
        await authCollection.save();
        return authCollection;
    }

};
export const setNewPassword = async(id, newPassword) => {
    if (newPassword != null && newPassword.length > 5 && newPassword.length < 13) {
        const encrypt = await hashPassword(newPassword);
        await User.findByIdAndUpdate(id, {
            $set: {
                password: encrypt
            }
        });
        return true;
    }
    return false;
}
export const updateUserById = async(data) => {
    let {
        mobile,
        email,
        isAdmin,
        firstName,
        lastName,
        id,
        clientId,
        prefix
    } = data;
    let user = {};
    if (id != null) {
        user = await User
            .findById(id)
            .populate("staffDetail")
            .exec();
    } else if (mobile != null) {
        user = await User
            .findOne({mobile: mobile})
            .populate("staffDetail")
            .exec();
    }

    if (user
        ?._id != null) {
        let userId = user._id
        await User.findByIdAndUpdate(userId, {
            $set: {
                mobile: mobile,
                email: email,
                isAdmin: isAdmin,
                firstName: firstName,
                lastName: lastName,
                prefix:prefix
            }
        });

        await updsertLinkedClientUser(userId, clientId, isAdmin, User.isDeleted);

        return user;

    } else {
        const encrypt = await hashPassword(generateId(8));
        const userCollection = new User({
            email: email, mobile: mobile, password: encrypt,
            // isAdmin: isAdmin,
            firstName: firstName,
            lastName: lastName,
            prefix: prefix,
        });
        await userCollection.save();

        const clientUser = new ClientUser({userId: userCollection._id, isAdmin: isAdmin, clinic: clientId})
        clientUser.save();
        return userCollection;
    }
}

const updsertLinkedClientUser = async(userId, clientId, isAdmin, isDeleted) => {
    if (userId != null && clientId != null) {
        var linkedClientUser = await ClientUser
            .findOne({userId: userId, clinic: clientId})
            .exec();
        if (linkedClientUser == null || linkedClientUser == undefined) {
            const clientUser = new ClientUser({userId: userId, isAdmin: isAdmin, clinic: clientId})
            clientUser.save();
        }
    }
}

export const generatePasswordHelper = async(data) => {
    let {phone, password} = data
    const user = await User.findOne({mobile: phone})
    let _id = user._id
    const encrypt = await hashPassword(password);
    return await User.findByIdAndUpdate(_id, {
        $set: {
            password: encrypt
        }
    });
}

function generateId(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        // Use cryptographically secure random number generation
        result += characters.charAt(crypto.randomInt(0, charactersLength));
        counter += 1;
    }
    return result;
}

const hashPassword = async(password) => {
        const hash = await bcrypt.hash(password, 10);
        return hash;
};

export const getUser = async(id) => {
        const user = await User
            .findById(id)
            .exec();
        return user;
}

export const getUserAndPrevilege = async(id) => {
        const user = await User
            .findById(id)
            .populate({
                path: "linkedClinics",
                select: {
                    clinic: 1,
                    isAdmin: 1
                }
            })
            .exec();
        return user;
}