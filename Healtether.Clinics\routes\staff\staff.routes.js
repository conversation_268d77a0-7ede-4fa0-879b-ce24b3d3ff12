import { Router } from "express";
import { validationResult } from "express-validator";
import {
  checkStaffMobileExists,
  deleteStaff,
  getDoctorsByClinic,
  getDoctorsWithAvailableTime,
  getDoctorTimeSlot,
  getStaff,
  getStaffOverview,
  searchStaffName,
  staffUpsert,
  updateDoc,
} from "../../controllers/staffs/staffs.controller.js";

import {
  validateUpsertStaff,
  validateStaffById,
  validateAllDoctors,
  validateStaffOverview,
  validateSearchStaffName,
  validateGetDoctorsWithTime,
  validateCheckMobileNumber,
  ValidateGetDoctorTimeSlot,
} from "./../../validation/staff/staff.validation.js";
import multer from "multer";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";

const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit per file
    files: 10, // Maximum 10 files per request
    fieldSize: 1024 * 1024, // 1MB limit for field values
    fields: 50 // Maximum 50 fields per request
  }
});

const staff = Router();
/**
 * @swagger
 * /staff/upsert:
 *   post:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Upsert staff information
 *     description: Add or update staff details. If the staff ID exists, the staff data will be updated. If not, a new staff entry will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               data:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The ID of the staff (if updating).
 *                   staffId:
 *                     type: string
 *                     description: Unique identifier for the staff within the clinic.
 *                   firstName:
 *                     type: string
 *                     description: First name of the staff.
 *                   lastName:
 *                     type: string
 *                     description: Last name of the staff.
 *                   specialisation:
 *                     type: string
 *                     description: Specialization of the staff.
 *                   isDoctor:
 *                     type: boolean
 *                     description: Whether the staff is a doctor.
 *                   isAdmin:
 *                     type: boolean
 *                     description: Whether the staff has admin privileges.
 *                   age:
 *                     type: number
 *                     description: Age of the staff.
 *                   birthday:
 *                     type: string
 *                     format: date
 *                     description: Birthday of the staff.
 *                   gender:
 *                     type: string
 *                     description: Gender of the staff.
 *                   mobile:
 *                     type: string
 *                     description: Mobile number of the staff (must be unique within clinic).
 *                   email:
 *                     type: string
 *                     description: Email of the staff.
 *                   address:
 *                     type: object
 *                     description: Address of the staff.
 *                   hprId:
 *                     type: string
 *                     description: Healthcare Professional ID.
 *                   documentType:
 *                     type: string
 *                     description: Type of document submitted by the staff.
 *                   documentNumber:
 *                     type: string
 *                     description: Document number of the submitted document.
 *                   upiId:
 *                     type: string
 *                     description: UPI ID for staff payment details.
 *                   bankName:
 *                     type: string
 *                     description: Bank name for staff payment details.
 *                   accountName:
 *                     type: string
 *                     description: Account name for staff payment details.
 *                   account:
 *                     type: string
 *                     description: Bank account number for staff.
 *                   ifsc:
 *                     type: string
 *                     description: IFSC code for bank details.
 *                   clientId:
 *                     type: string
 *                     description: ID of the client/clinic.
 *                   profilepic:
 *                     type: string
 *                     description: URL or data for staff profile picture.
 *                   documents:
 *                     type: array
 *                     description: Array of staff documents.
 *                   availableTimeSlot:
 *                     type: object
 *                     description: Available time slots mapped by weekdays.
 *     responses:
 *       200:
 *         description: Staff information upserted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: number
 *                   example: 0
 *                 message:
 *                   type: string
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Bad request. Invalid data or duplicate entry.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: number
 *                   example: 400
 *                 message:
 *                   type: string
 *                   enum: [mobile-duplicate, name-duplicate, staffId-duplicate]
 *                   description: Specific error message indicating the type of duplicate entry.
 *                 success:
 *                   type: boolean
 *                   example: false
 *       500:
 *         description: Internal server error.
 */
staff.post("/upsert", authorizationCheck, validateUpsertStaff,
  async (req, res, next) => {
    try {
        return await staffUpsert(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /staff/updatedocument:
 *   post:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Update staff profile and document files
 *     description: Uploads staff profile and document files to Azure Blob Storage. Updates the document names and blobs in storage.
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               profile:
 *                 type: string
 *                 format: binary
 *                 description: Profile image file.
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Array of document files.
 *               documentName:
 *                 type: string
 *                 description: JSON string containing document names and blob names.
 *               profileName:
 *                 type: string
 *                 description: Name of the profile picture blob in Azure.
 *     responses:
 *       200:
 *         description: Files uploaded successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates whether the operation was successful.
 *       400:
 *         description: Bad request. Invalid file data or document names.
 *       500:
 *         description: Internal server error.
 */
staff.post(
  "/updatedocument",
  upload.fields([
    {
      name: "profile",
      maxCount: 1,
    },
    {
      name: "documents",
      maxCount: 5,
    },
  ]),
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /staff/getstaffs:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Get staff overview with pagination, filtering, and sorting
 *     description: Retrieves a list of staff members based on pagination, filtering by keyword and status, and sorting by specified fields.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the client (clinic) to retrieve staff data for.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         required: false
 *         description: The page number for pagination (default is 1).
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *         required: false
 *         description: The number of records per page (default is 10).
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         required: false
 *         description: A search keyword to filter staff members by name or other fields.
 *       - in: query
 *         name: sortby
 *         schema:
 *           type: string
 *         required: false
 *         description: The field to sort the results by (e.g., firstName, lastName).
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         required: false
 *         description: Sort direction, either ascending (asc) or descending (desc).
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *         required: false
 *         description: Filter staff by their current status (active or inactive).
 *     responses:
 *       200:
 *         description: Successfully retrieved the staff overview.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       staffId:
 *                         type: string
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       specialization:
 *                         type: string
 *                       isDoctor:
 *                         type: boolean
 *                       status:
 *                         type: string
 *                         enum: [active, inactive]
 *                       clinic:
 *                         type: string
 *                         description: The ID of the clinic the staff belongs to.
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalRecords:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get(
  "/getstaffs",
  authorizationCheck,
  validateStaffOverview,
  
  async (req, res, next) => {
    try {
        return await getStaffOverview(req, res);
    }
    catch (e) {
        next(e)
    }
}
);



/**
 * @swagger
 * /staff/getstaff:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Get staff details by ID
 *     description: Retrieve detailed information of a staff member by their ID.
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the staff member to fetch.
 *     responses:
 *       200:
 *         description: Successfully retrieved staff details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 staffId:
 *                   type: string
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 specialization:
 *                   type: string
 *                 isDoctor:
 *                   type: boolean
 *                 clinic:
 *                   type: string
 *                   description: The ID of the clinic the staff belongs to.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get("/getstaff", authorizationCheck, validateStaffById, getStaff);


/**
 * @swagger
 * /staff/deletestaff:
 *   delete:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Delete a staff member
 *     description: Deletes a staff member by their ID and sends a notification after deletion.
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the staff member to delete.
 *       - in: query
 *         name: Notificationkey
 *         schema:
 *           type: string
 *         required: true
 *         description: Notification key to send the deletion notification.
 *       - in: query
 *         name: user
 *         schema:
 *           type: object
 *         required: true
 *         description: Object containing the user performing the deletion.
 *         properties:
 *           id:
 *             type: string
 *             description: ID of the user performing the deletion action.
 *           name:
 *             type: string
 *             description: Name of the user performing the deletion action.
 *     responses:
 *       200:
 *         description: Staff member deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: Empty object returned upon success.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.delete(
  "/deletestaff",
  authorizationCheck,
  validateStaffById,
  async (req, res, next) => {
    try {
        return await deleteStaff(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /staff/searchstaffname:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Search staff by name
 *     description: Search for staff members by their name within a specified clinic.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the clinic to search within.
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         required: true
 *         description: The name of the staff member to search for.
 *       - in: query
 *         name: size
 *         schema:
 *           type: integer
 *         required: false
 *         description: The number of staff members to return.
 *     responses:
 *       200:
 *         description: Successfully retrieved staff members matching the search criteria.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       staffId:
 *                         type: string
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       clinic:
 *                         type: string
 *                         description: The ID of the clinic the staff belongs to.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get(
  "/searchstaffname",
  authorizationCheck,
  validateSearchStaffName,
  async (req, res, next) => {
    try {
        return await searchStaffName(req, res);
    }
    catch (e) {
        next(e)
    }
}
);


/**
 * @swagger
 * /staff/getdoctorsbyclinic:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Get doctors by clinic ID
 *     description: Retrieve a list of doctors working in a specific clinic.
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the clinic to retrieve doctors from.
 *     responses:
 *       200:
 *         description: Successfully retrieved list of doctors.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   doctorId:
 *                     type: string
 *                   firstName:
 *                     type: string
 *                   lastName:
 *                     type: string
 *                   specialization:
 *                     type: string
 *                   clinic:
 *                     type: string
 *                     description: The ID of the clinic the doctor belongs to.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get(
  "/getdoctorsbyclinic",
  authorizationCheck,
  validateAllDoctors,
  async (req, res, next) => {
    try {
        return await getDoctorsByClinic(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /staff/getdoctorswithtime:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Get doctors with available time slots by clinic ID
 *     description: Retrieve a list of doctors who have available time slots in a specific clinic.
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the clinic to retrieve doctors with available time slots from.
 *     responses:
 *       200:
 *         description: Successfully retrieved list of doctors with available time slots.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   doctorId:
 *                     type: string
 *                   firstName:
 *                     type: string
 *                   lastName:
 *                     type: string
 *                   specialization:
 *                     type: string
 *                   availableTimeSlots:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         day:
 *                           type: string
 *                         timeSlots:
 *                           type: array
 *                           items:
 *                             type: string
 *                   clinic:
 *                     type: string
 *                     description: The ID of the clinic the doctor belongs to.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get(
  "/getdoctorswithtime",
  authorizationCheck,
  validateGetDoctorsWithTime,
  async (req, res, next) => {
    try {
        return await getDoctorsWithAvailableTime(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /staff/checkmobilenumber:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Check if staff mobile number exists
 *     description: Verify if a staff member's mobile number already exists in the system.
 *     parameters:
 *       - in: query
 *         name: mobile
 *         schema:
 *           type: string
 *         required: true
 *         description: The mobile number to check for existence.
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         required: false
 *         description: The ID of the staff member (used to exclude the current staff from the check).
 *     responses:
 *       200:
 *         description: Successfully checked if the mobile number exists.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exists:
 *                   type: boolean
 *                   description: Whether the mobile number exists in the system.
 *       400:
 *         description: Bad request, missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
staff.get(
  "/checkmobilenumber",
  authorizationCheck,
  validateCheckMobileNumber,
  
  async (req, res, next) => {
    try {
        return await checkStaffMobileExists(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /staff/gettimeslotofdoctor:
 *   get:
 *     tags:
 *       - staff (mobile)
 *       - staff
 *     summary: Retrieve available time slots for a doctor
 *     description: Fetches available time slots for a doctor based on the doctor ID provided in the query string.
 *     security:
 *       - bearerAuth: []  # Adds Bearer token authentication
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the doctor to retrieve time slots for
 *         example: "60d5ec49c67e4d8f889a4d50"
 *     responses:
 *       200:
 *         description: Available doctor time slots retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       timeSlot:
 *                         type: string
 *                         example: "10:00 AM - 11:00 AM"
 *                       available:
 *                         type: boolean
 *                         example: true
 *       400:
 *         description: Bad request, doctor ID is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Invalid doctor ID"
 *       404:
 *         description: Doctor not found or no available time slots
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Doctor not found or no available time slots"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
staff.get(
  "/gettimeslotofdoctor",
  authorizationCheck,
  ValidateGetDoctorTimeSlot,
  
  async (req, res, next) => {
    try {
        return await getDoctorTimeSlot(req, res);
    }
    catch (e) {
        next(e)
    }
}
);
export default staff;
