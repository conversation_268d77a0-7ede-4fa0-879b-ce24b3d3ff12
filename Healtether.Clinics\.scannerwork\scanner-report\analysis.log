Plugins:
Bundled analyzers:
  - JaCoCo 1.3.0.1538 (jacoco)
  - IaC Code Quality and Security 1.47.0.15287 (iac)
  - Text Code Quality and Security 2.24.1.6530 (text)
  - Clean as You Code 2.4.0.2018 (cayc)
Global server settings:
  - sonar.azureresourcemanager.file.suffixes=.bicep
  - sonar.core.id=9B767396-AZeC3e30XWKefBJ_c7mf
  - sonar.core.startTime=2025-06-20T09:42:27+0000
  - sonar.cs.file.suffixes=.cs,.razor
  - sonar.css.file.suffixes=.css,.less,.scss,.sass
  - sonar.docker.file.patterns=Dockerfile,*.dockerfile
  - sonar.flex.file.suffixes=as
  - sonar.forceAuthentication=true
  - sonar.go.file.suffixes=.go
  - sonar.html.file.suffixes=.html,.xhtml,.cshtml,.vbhtml,.aspx,.ascx,.rhtml,.erb,.shtm,.shtml,.cmp,.twig
  - sonar.ipynb.file.suffixes=ipynb
  - sonar.java.file.suffixes=.java,.jav
  - sonar.java.jvmframeworkconfig.file.patterns=**/src/main/resources/**/*app*.properties,**/src/main/resources/**/*app*.yaml,**/src/main/resources/**/*app*.yml
  - sonar.javascript.file.suffixes=.js,.jsx,.cjs,.mjs,.vue
  - sonar.json.file.suffixes=.json
  - sonar.jsp.file.suffixes=.jsp,.jspf,.jspx
  - sonar.kotlin.file.suffixes=.kt,.kts
  - sonar.multi-quality-mode.enabled=true
  - sonar.php.file.suffixes=php,php3,php4,php5,phtml,inc
  - sonar.python.file.suffixes=py
  - sonar.qualityProfiles.allowDisableInheritedRules=true
  - sonar.ruby.file.suffixes=.rb
  - sonar.rust.file.suffixes=.rs
  - sonar.scala.file.suffixes=.scala
  - sonar.terraform.file.suffixes=.tf
  - sonar.typescript.file.suffixes=.ts,.tsx,.cts,.mts
  - sonar.vbnet.file.suffixes=.vb
  - sonar.xml.file.suffixes=.xml,.xsd,.xsl,.config
  - sonar.yaml.file.suffixes=.yaml,.yml
Project server settings:
  - sonar.azureresourcemanager.file.suffixes=.bicep
  - sonar.cs.file.suffixes=.cs,.razor
  - sonar.css.file.suffixes=.css,.less,.scss,.sass
  - sonar.docker.file.patterns=Dockerfile,*.dockerfile
  - sonar.flex.file.suffixes=as
  - sonar.go.file.suffixes=.go
  - sonar.html.file.suffixes=.html,.xhtml,.cshtml,.vbhtml,.aspx,.ascx,.rhtml,.erb,.shtm,.shtml,.cmp,.twig
  - sonar.ipynb.file.suffixes=ipynb
  - sonar.java.file.suffixes=.java,.jav
  - sonar.java.jvmframeworkconfig.file.patterns=**/src/main/resources/**/*app*.properties,**/src/main/resources/**/*app*.yaml,**/src/main/resources/**/*app*.yml
  - sonar.javascript.file.suffixes=.js,.jsx,.cjs,.mjs,.vue
  - sonar.json.file.suffixes=.json
  - sonar.jsp.file.suffixes=.jsp,.jspf,.jspx
  - sonar.kotlin.file.suffixes=.kt,.kts
  - sonar.php.file.suffixes=php,php3,php4,php5,phtml,inc
  - sonar.python.file.suffixes=py
  - sonar.ruby.file.suffixes=.rb
  - sonar.rust.file.suffixes=.rs
  - sonar.scala.file.suffixes=.scala
  - sonar.terraform.file.suffixes=.tf
  - sonar.typescript.file.suffixes=.ts,.tsx,.cts,.mts
  - sonar.vbnet.file.suffixes=.vb
  - sonar.xml.file.suffixes=.xml,.xsd,.xsl,.config
  - sonar.yaml.file.suffixes=.yaml,.yml
Project scanner properties:
  - sonar.analysis.mode=publish
  - sonar.coverage.exclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js,**/node_modules/**
  - sonar.cpd.exclusions=**/*test.js,**/*spec.js
  - sonar.exclusions=**/node_modules/**,**/coverage/**,**/dist/**,**/build/**,**/*.min.js,**/vendor/**,**/third-party/**,**/*.log,**/logs/**,**/temp/**,**/tmp/**,**/.env,**/*.env
  - sonar.host.url=http://localhost:9000
  - sonar.inclusions=**/*.js,**/*.jsx,**/*.ts,**/*.tsx,**/*.json
  - sonar.issue.ignore.multicriteria=e1,e2
  - sonar.issue.ignore.multicriteria.e1.resourceKey=**/helper/fhir/**
  - sonar.issue.ignore.multicriteria.e1.ruleKey=javascript:S5332
  - sonar.issue.ignore.multicriteria.e2.resourceKey=**/.env
  - sonar.issue.ignore.multicriteria.e2.ruleKey=secrets:S6706
  - sonar.javascript.environments=node,browser
  - sonar.javascript.file.suffixes=.js,.jsx
  - sonar.javascript.globals=global,process,Buffer,__dirname,__filename,module,require,exports,console,io
  - sonar.javascript.lcov.reportPaths=coverage/lcov.info
  - sonar.json.file.suffixes=.json
  - sonar.projectBaseDir=C:\Users\<USER>\Documents\augment-projects\web-app\Healtether.Clinics
  - sonar.projectDescription=Healthcare Clinics Management API - Static Code Analysis
  - sonar.projectKey=healtether-clinics-api
  - sonar.projectName=Healtether Clinics API
  - sonar.projectVersion=1.0.0
  - sonar.qualitygate.wait=true
  - sonar.scanner.apiBaseUrl=http://localhost:9000/api/v2
  - sonar.scanner.app=ScannerNpm
  - sonar.scanner.appVersion=4.3.0
  - sonar.scanner.arch=x64
  - sonar.scanner.bootstrapStartTime=1750432435783
  - sonar.scanner.internal.isSonarCloud=false
  - sonar.scanner.os=win32
  - sonar.scanner.wasEngineCacheHit=true
  - sonar.scanner.wasJreCacheHit=hit
  - sonar.security.exclusions=**/helper/fhir/**/*.js,**/utils/fhir.constants.js,**/temp_invoice_report.js,**/.env,**/*.env
  - sonar.security.hotspots.inheritFromParent=true
  - sonar.sourceEncoding=UTF-8
  - sonar.sources=.
  - sonar.test.exclusions=node_modules/**
  - sonar.test.inclusions=**/*test.js,**/*spec.js,**/*.test.js,**/*.spec.js
  - sonar.tests=__tests__
  - sonar.text.exclusions=**/.env,**/*.env
  - sonar.token=******
  - sonar.typescript.file.suffixes=.ts,.tsx
  - sonar.userHome=C:\Users\<USER>\.sonar
  - sonar.working.directory=.scannerwork
