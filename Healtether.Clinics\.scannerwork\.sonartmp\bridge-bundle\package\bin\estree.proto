syntax = "proto3";
// Generated for @types/estree version: 1.0.5
// Note: this file was manually modified, to reach a working state faster.
// We should eventually adapt the generator once we are happy with the exact structure.
option java_package="org.sonar.plugins.javascript.bridge.protobuf";
option java_multiple_files = true;

message SourceLocation {
  string source = 1;
  Position start = 2;
  Position end = 3;
}
message Position {
  int32 line = 1;
  int32 column = 2;
}

enum NodeType {
  ProgramType = 0;
  ExportAllDeclarationType = 1;
  IdentifierType = 2;
  ExportDefaultDeclarationType = 3;
  YieldExpressionType = 4;
  UpdateExpressionType = 5;
  UnaryExpressionType = 6;
  ThisExpressionType = 7;
  TemplateLiteralType = 8;
  TaggedTemplateExpressionType = 9;
  SequenceExpressionType = 10;
  ObjectExpressionType = 11;
  SpreadElementType = 12;
  PropertyType = 13;
  AssignmentPatternType = 14;
  RestElementType = 15;
  ArrayPatternType = 16;
  ObjectPatternType = 17;
  PrivateIdentifierType = 18;
  NewExpressionType = 19;
  SuperType = 20;
  MetaPropertyType = 21;
  MemberExpressionType = 22;
  LogicalExpressionType = 23;
  ImportExpressionType = 24;
  BlockStatementType = 25;
  ConditionalExpressionType = 26;
  ClassExpressionType = 27;
  ClassBodyType = 28;
  StaticBlockType = 29;
  PropertyDefinitionType = 30;
  MethodDefinitionType = 31;
  ChainExpressionType = 32;
  CallExpressionType = 33;
  BinaryExpressionType = 34;
  AwaitExpressionType = 35;
  AssignmentExpressionType = 36;
  ArrowFunctionExpressionType = 37;
  ArrayExpressionType = 38;
  ClassDeclarationType = 39;
  FunctionDeclarationType = 40;
  ExportNamedDeclarationType = 41;
  ExportSpecifierType = 42;
  VariableDeclarationType = 43;
  VariableDeclaratorType = 44;
  ImportDeclarationType = 45;
  ImportNamespaceSpecifierType = 46;
  ImportDefaultSpecifierType = 47;
  ImportSpecifierType = 48;
  ForOfStatementType = 49;
  ForInStatementType = 50;
  ForStatementType = 51;
  DoWhileStatementType = 52;
  WhileStatementType = 53;
  TryStatementType = 54;
  CatchClauseType = 55;
  ThrowStatementType = 56;
  SwitchStatementType = 57;
  SwitchCaseType = 58;
  IfStatementType = 59;
  ContinueStatementType = 60;
  BreakStatementType = 61;
  LabeledStatementType = 62;
  ReturnStatementType = 63;
  WithStatementType = 64;
  DebuggerStatementType = 65;
  EmptyStatementType = 66;
  ExpressionStatementType = 67;
  LiteralType = 68;
  TemplateElementType = 69;
  FunctionExpressionType = 70;
  TSExportAssignmentType = 71;
  TSImportEqualsDeclarationType = 72;
  TSQualifiedNameType = 73;
  TSExternalModuleReferenceType = 74;
  TSModuleBlockType = 75;
  TSModuleDeclarationType = 76;
  TSParameterPropertyType = 77;
  TSTypeAliasDeclarationType = 78;
  TSInterfaceDeclarationType = 79;
  TSEmptyBodyFunctionExpressionType = 80;
  TSEnumDeclarationType = 81;
  TSDeclareFunctionType = 82;
  TSAbstractMethodDefinitionType = 83;
  UnknownNodeType = 1000;
}
message Node {
  NodeType type = 1;
  SourceLocation loc = 2;
  oneof node {
    Program program = 3;
    ExportAllDeclaration exportAllDeclaration = 4;
    Identifier identifier = 5;
    ExportDefaultDeclaration exportDefaultDeclaration = 6;
    YieldExpression yieldExpression = 7;
    UpdateExpression updateExpression = 8;
    UnaryExpression unaryExpression = 9;
    ThisExpression thisExpression = 10;
    TemplateLiteral templateLiteral = 11;
    TaggedTemplateExpression taggedTemplateExpression = 12;
    SequenceExpression sequenceExpression = 13;
    ObjectExpression objectExpression = 14;
    SpreadElement spreadElement = 15;
    Property property = 16;
    AssignmentPattern assignmentPattern = 17;
    RestElement restElement = 18;
    ArrayPattern arrayPattern = 19;
    ObjectPattern objectPattern = 20;
    PrivateIdentifier privateIdentifier = 21;
    NewExpression newExpression = 22;
    Super super = 23;
    MetaProperty metaProperty = 24;
    MemberExpression memberExpression = 25;
    LogicalExpression logicalExpression = 26;
    ImportExpression importExpression = 27;
    BlockStatement blockStatement = 28;
    ConditionalExpression conditionalExpression = 29;
    ClassExpression classExpression = 30;
    ClassBody classBody = 31;
    StaticBlock staticBlock = 32;
    PropertyDefinition propertyDefinition = 33;
    MethodDefinition methodDefinition = 34;
    ChainExpression chainExpression = 35;
    CallExpression callExpression = 36;
    BinaryExpression binaryExpression = 37;
    AwaitExpression awaitExpression = 38;
    AssignmentExpression assignmentExpression = 39;
    ArrowFunctionExpression arrowFunctionExpression = 40;
    ArrayExpression arrayExpression = 41;
    ClassDeclaration classDeclaration = 42;
    FunctionDeclaration functionDeclaration = 43;
    ExportNamedDeclaration exportNamedDeclaration = 44;
    ExportSpecifier exportSpecifier = 45;
    VariableDeclaration variableDeclaration = 46;
    VariableDeclarator variableDeclarator = 47;
    ImportDeclaration importDeclaration = 48;
    ImportNamespaceSpecifier importNamespaceSpecifier = 49;
    ImportDefaultSpecifier importDefaultSpecifier = 50;
    ImportSpecifier importSpecifier = 51;
    ForOfStatement forOfStatement = 52;
    ForInStatement forInStatement = 53;
    ForStatement forStatement = 54;
    DoWhileStatement doWhileStatement = 55;
    WhileStatement whileStatement = 56;
    TryStatement tryStatement = 57;
    CatchClause catchClause = 58;
    ThrowStatement throwStatement = 59;
    SwitchStatement switchStatement = 60;
    SwitchCase switchCase = 61;
    IfStatement ifStatement = 62;
    ContinueStatement continueStatement = 63;
    BreakStatement breakStatement = 64;
    LabeledStatement labeledStatement = 65;
    ReturnStatement returnStatement = 66;
    WithStatement withStatement = 67;
    DebuggerStatement debuggerStatement = 68;
    EmptyStatement emptyStatement = 69;
    ExpressionStatement expressionStatement = 70;
    Literal literal = 71;
    TemplateElement templateElement = 72;
    FunctionExpression functionExpression = 73;
    ExportAssignment exportAssignment = 74;
    TSImportEqualsDeclaration tSImportEqualsDeclaration = 75;
    TSQualifiedName tSQualifiedName = 76;
    TSExternalModuleReference tSExternalModuleReference = 77;
    TSModuleBlock tSModuleBlock = 78;
    TSModuleDeclaration tSModuleDeclaration = 79;
    TSParameterProperty tSParameterProperty = 80;
    UnknownNode unknownNode = 1000;
  }
}

message Program {
  string sourceType = 1;
  repeated Node body = 2;
}
message ExportAllDeclaration {
  optional Node exported = 1;
  Node source = 2;
}
message Literal {
  string raw = 1;
  optional string bigint = 2;
  optional string pattern = 3;
  optional string flags = 4;
  oneof value {
    string valueString = 5;
    bool valueBoolean = 6;
    int32 valueNumber = 7;
  }
}
message Identifier {
  string name = 1;
}
message ExportDefaultDeclaration {
  Node declaration = 1;
}
message YieldExpression {
  optional Node argument = 1;
  bool delegate = 2;
}
message UpdateExpression {
  string operator = 1;
  Node argument = 2;
  bool prefix = 3;
}
message UnaryExpression {
  string operator = 1;
  bool prefix = 2;
  Node argument = 3;
}
message ThisExpression {
}
message TemplateLiteral {
  repeated Node quasis = 1;
  repeated Node expressions = 2;
}
message TaggedTemplateExpression {
  Node tag = 1;
  Node quasi = 2;
}
message SequenceExpression {
  repeated Node expressions = 1;
}
message ObjectExpression {
  repeated Node properties = 1;
}
message SpreadElement {
  Node argument = 1;
}
message Property {
  Node key = 1;
  Node value = 2;
  string kind = 3;
  bool method = 4;
  bool shorthand = 5;
  bool computed = 6;
}
message AssignmentPattern {
  Node left = 1;
  Node right = 2;
}
message RestElement {
  Node argument = 1;
}
message ArrayPattern {
  repeated ArrayElement elements = 1;
}
message ObjectPattern {
  repeated Node properties = 1;
}
message PrivateIdentifier {
  string name = 1;
}
message NewExpression {
  Node callee = 1;
  repeated Node arguments = 2;
}
message Super {
}
message MetaProperty {
  Node meta = 1;
  Node property = 2;
}
message MemberExpression {
  Node object = 1;
  Node property = 2;
  bool computed = 3;
  bool optional = 4;
}
message LogicalExpression {
  string operator = 1;
  Node left = 2;
  Node right = 3;
}
message ImportExpression {
  Node source = 1;
}
message BlockStatement {
  repeated Node body = 1;
}
message ConditionalExpression {
  Node test = 1;
  Node alternate = 2;
  Node consequent = 3;
}
message ClassExpression {
  optional Node id = 1;
  optional Node superClass = 2;
  Node body = 3;
}
message ClassBody {
  repeated Node body = 1;
}
message StaticBlock {
  repeated Node body = 1;
}
message PropertyDefinition {
  Node key = 1;
  optional Node value = 2;
  bool computed = 3;
  bool static = 4;
}
message MethodDefinition {
  Node key = 1;
  Node value = 2;
  string kind = 3;
  bool computed = 4;
  bool static = 5;
}
message ChainExpression {
  Node expression = 1;
}
message CallExpression {
  bool optional = 1;
  Node callee = 2;
  repeated Node arguments = 3;
}
message BinaryExpression {
  string operator = 1;
  Node left = 2;
  Node right = 3;
}
message AwaitExpression {
  Node argument = 1;
}
message AssignmentExpression {
  string operator = 1;
  Node left = 2;
  Node right = 3;
}
message ArrowFunctionExpression {
  bool expression = 1;
  Node body = 2;
  repeated Node params = 3;
  optional bool generator = 4;
  optional bool async = 5;
}
message ArrayExpression {
  repeated ArrayElement elements = 1;
}
message ArrayElement {
  optional Node element = 1;
}
message ClassDeclaration {
  optional Node id = 1;
  optional Node superClass = 2;
  Node body = 3;
}
message FunctionDeclaration {
  Node id = 1;
  Node body = 2;
  repeated Node params = 3;
  optional bool generator = 4;
  optional bool async = 5;
}
message ExportNamedDeclaration {
  optional Node declaration = 1;
  repeated Node specifiers = 2;
  optional Node source = 3;
}
message ExportSpecifier {
  Node exported = 1;
  Node local = 2;
}
message VariableDeclaration {
  repeated Node declarations = 1;
  string kind = 2;
}
message VariableDeclarator {
  Node id = 1;
  optional Node init = 2;
}
message ImportDeclaration {
  repeated Node specifiers = 1;
  Node source = 2;
}
message ImportNamespaceSpecifier {
  Node local = 1;
}
message ImportDefaultSpecifier {
  Node local = 1;
}
message ImportSpecifier {
  Node imported = 1;
  Node local = 2;
}
message ForOfStatement {
  bool await = 1;
  Node left = 2;
  Node right = 3;
  Node body = 4;
}
message ForInStatement {
  Node left = 1;
  Node right = 2;
  Node body = 3;
}
message ForStatement {
  optional Node init = 1;
  optional Node test = 2;
  optional Node update = 3;
  Node body = 4;
}
message DoWhileStatement {
  Node body = 1;
  Node test = 2;
}
message WhileStatement {
  Node test = 1;
  Node body = 2;
}
message TryStatement {
  Node block = 1;
  optional Node handler = 2;
  optional Node finalizer = 3;
}
message CatchClause {
  optional Node param = 1;
  Node body = 2;
}
message ThrowStatement {
  Node argument = 1;
}
message SwitchStatement {
  Node discriminant = 1;
  repeated Node cases = 2;
}
message SwitchCase {
  optional Node test = 1;
  repeated Node consequent = 2;
}
message IfStatement {
  Node test = 1;
  Node consequent = 2;
  optional Node alternate = 3;
}
message ContinueStatement {
  optional Node label = 1;
}
message BreakStatement {
  optional Node label = 1;
}
message LabeledStatement {
  Node label = 1;
  Node body = 2;
}
message ReturnStatement {
  optional Node argument = 1;
}
message WithStatement {
  Node object = 1;
  Node body = 2;
}
message DebuggerStatement {
}
message EmptyStatement {
}
message ExpressionStatement {
  Node expression = 1;
  optional string directive = 2;
}
message TemplateElement {
  bool tail = 1;
  optional string cooked = 2;
  string raw = 3;
}
message FunctionExpression {
  optional Node id = 1;
  Node body = 2;
  repeated Node params = 3;
  optional bool generator = 4;
  optional bool async = 5;
}
message ExportAssignment {
  Node expression = 1;
}
message TSImportEqualsDeclaration {
  Node id = 1;
  Node moduleReference = 2;
  string importKind = 3;
}
message TSQualifiedName {
  Node left = 1;
  Node right = 2;
}
message TSExternalModuleReference {
  Node expression = 1;
}
message TSModuleBlock {
  repeated Node body = 1;
}
message TSModuleDeclaration {
  Node id = 1;
  optional Node body = 2;
  string kind = 3;
}
message TSParameterProperty {
  optional string accessibility = 1;
  bool readonly = 2;
  Node parameter = 3;
}
message UnknownNode {
}
