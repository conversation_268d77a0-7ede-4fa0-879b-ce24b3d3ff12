import { Router } from "express";
import {
  validateDeletePatient,
  validateAddOrUpdatePatientDetails,
  validateGetPatient,
  validateGetPatientAppointmentOrMedical,
  validateGetPatientOverview,
  validateSearchPatientMobile,
  validateUpdateDocumentOrRecords,
} from "./../../validation/patient/patient.validation.js";
import {
  getPatient,
  getPatientOverview,
  updateDoc,
  searchPatientMobile,
  addPatientDetails,
  updatePatientDetails,
  getPatientLatestAppointment,
  getPatientWithAllAppointment,
  deletePatient,
  getPatientWithAllMedical,
  addPatientProfileWithAbhaDetails,
} from "../../controllers/patients/patients.controller.js";
import multer from "multer";
import { authorizationCheck } from "../../middleware/jwt_authorization.js";
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit per file
    files: 10, // Maximum 10 files per request
    fieldSize: 1024 * 1024, // 1MB limit for field values
    fields: 50 // Maximum 50 fields per request
  }
});

const patient = Router();

/**
 * @swagger
 * /patient/updatedocument:
 *   post:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Update document for a patient
 *     description: |
 *       Uploads new documents for a patient associated with a specified client ID.
 *       This can include documents like profile pictures, reports, etc., stored in Azure storage.
 *        
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               clientId:
 *                 type: string
 *                 example: "**********abcdef12345678"
 *               documentName:
 *                 type: string
 *                 description: 
 *                   A JSON string containing document names and blob names
 *                   for Azure storage. Each document should have a "name" and
 *                   corresponding "blobName" which represents the file stored in Azure.
 *                 example: '[{"fileName": "Document1", "blobName": "doc1.png"}, {"fileName": "Document2", "blobName": "doc2.pdf"}]'
 *               profileName:
 *                 type: string
 *                 description: The name of the patient's profile picture file.
 *                 example: "profile_picture.png"
 *     responses:
 *       200:
 *         description: Documents updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Unexpected error during document upload.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unexpected error"
 */
patient.post(
  "/updatedocument",
  authorizationCheck,
  upload.fields([
    {
      name: "profile",
      maxCount: 1,
    },
    {
      name: "documents",
      maxCount: 5,
    },
  ]),
  validateUpdateDocumentOrRecords,
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
});


patient.post(
  "/updatemedicalrecord",
  authorizationCheck,
  upload.fields([
    {
      name: "profile",
      maxCount: 1,
    },
    {
      name: "medicalRecords",
      maxCount: 5,
    },
  ]),
  validateUpdateDocumentOrRecords,
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
});



patient.post(
  "/updateprocedurerecord",
  authorizationCheck,
  upload.fields([
    {
      name: "profile",
      maxCount: 1,
    },
    {
      name: "procedureRecords",
      maxCount: 5,
    },
  ]),
  validateUpdateDocumentOrRecords,
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
});


patient.post(
  "/updateprescriptionrecord",
  authorizationCheck,
  upload.fields([
    {
      name: "profile",
      maxCount: 1,
    },
    {
      name: "prescriptionRecords",
      maxCount: 5,
    },
  ]),
  async (req, res, next) => {
    try {
        return await updateDoc(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/getpatients:
 *   get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Retrieve patient overview
 *     description: Fetches a paginated overview of patients associated with a specific client ID, with optional filtering and sorting.
 *     parameters:
 *       - in: query
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the client whose patient overview is being requested.
 *         example: "**********abcdef12345678"
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The page number for pagination (default is 1).
 *         example: 1
 *       - in: query
 *         name: size
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The number of records to return per page (default is 10).
 *         example: 10
 *       - in: query
 *         name: keyword
 *         required: false
 *         schema:
 *           type: string
 *         description: Keyword to filter patients based on their details (e.g., mobile, firstName, lastName, patientId).
 *         example: "John"
 *       - in: query
 *         name: sortby
 *         required: false
 *         schema:
 *           type: string
 *         description: Field by which to sort the results (e.g., "modified", "created").
 *         example: "name"
 *       - in: query
 *         name: direction
 *         required: false
 *         schema:
 *           type: string
 *           enum:
 *             - asc
 *             - desc
 *         description: Sort direction (asc or desc). Default is descending.
 *         example: "asc"
 *     responses:
 *       200:
 *         description: A successful response containing the patient overview data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: The unique ID of the patient.
 *                         example: "60d5ec49c67e4d8f889a4d65"
 *                       firstName:
 *                         type: string
 *                         description: First name of the patient.
 *                         example: "John"
 *                       lastName:
 *                         type: string
 *                         description: Last name of the patient.
 *                         example: "Doe"
 *                       mobile:
 *                         type: string
 *                         description: Mobile number of the patient.
 *                         example: "+**********"
 *                       gender:
 *                         type: string
 *                         description: Gender of the patient.
 *                         example: "Male"
 *                       age:
 *                         type: number
 *                         description: Age of the patient.
 *                         example: 30
 *                       patientId:
 *                         type: string
 *                         description: Unique patient ID.
 *                         example: "P123456"
 *                       appointments:
 *                         type: array
 *                         description: List of recent appointments for the patient.
 *                         items:
 *                           type: object
 *                           properties:
 *                             appointmentDate:
 *                               type: string
 *                               format: date-time
 *                               description: Date and time of the appointment.
 *                               example: "2024-12-01T10:00:00Z"
 *                             timeSlot:
 *                               type: string
 *                               description: Time slot of the appointment.
 *                               example: "10:00 AM - 11:00 AM"
 *                             reason:
 *                               type: string
 *                               description: Reason for the appointment.
 *                               example: "General Checkup"
 *                             doctorName:
 *                               type: string
 *                               description: Name of the doctor.
 *                               example: "Dr. Smith"
 *                             paymentStatus:
 *                               type: string
 *                               description: Payment status of the appointment.
 *                               example: "Paid"
 *                 totalCount:
 *                   type: integer
 *                   description: Total number of patients matching the query.
 *                   example: 100
 *       400:
 *         description: Bad request, invalid or missing parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Client ID is required"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while fetching patient data"
 */
patient.get(
  "/getpatients",
  authorizationCheck,
  validateGetPatientOverview,
  async (req, res, next) => {
    try {
        return await getPatientOverview(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/getpatient:
 *   get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Retrieve patient details
 *     description: Fetches detailed information about a specific patient by ID.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient to retrieve details for.
 *         example: "60c72b2f5f1b2c001c8e4e09"
 *     responses:
 *       200:
 *         description: A successful response containing patient details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                   description: First name of the patient.
 *                   example: "John"
 *                 lastName:
 *                   type: string
 *                   description: Last name of the patient.
 *                   example: "Doe"
 *                 age:
 *                   type: integer
 *                   description: Age of the patient.
 *                   example: 30
 *                 birthday:
 *                   type: string
 *                   format: date
 *                   description: Birth date of the patient.
 *                   example: "1993-07-21"
 *                 gender:
 *                   type: string
 *                   description: Gender of the patient.
 *                   example: "Male"
 *                 mobile:
 *                   type: string
 *                   description: Mobile number of the patient.
 *                   example: "**********"
 *                 email:
 *                   type: string
 *                   description: Email address of the patient.
 *                   example: "<EMAIL>"
 *                 patientId:
 *                   type: string
 *                   description: Unique patient ID.
 *                   example: "P123456"
 *                 address:
 *                   type: string
 *                   description: Address of the patient.
 *                   example: "123 Main St, Cityville, 123456"
 *                 height:
 *                   type: number
 *                   description: Height of the patient in centimeters.
 *                   example: 175
 *                 weight:
 *                   type: number
 *                   description: Weight of the patient in kilograms.
 *                   example: 70
 *                 abhaAddress:
 *                   type: string
 *                   description: ABHA address of the patient.
 *                   example: "john.doe@abdm"
 *                 abhaNumber:
 *                   type: string
 *                   description: ABHA number of the patient.
 *                   example: "**********12"
 *                 profilePic:
 *                   type: string
 *                   description: URL or path to the patient's profile picture.
 *                   example: "https://example.com/profile.jpg"
 *                 documentType:
 *                   type: string
 *                   description: Type of document provided by the patient.
 *                   example: "ID Card"
 *                 documentNumber:
 *                   type: string
 *                   description: Document number provided by the patient.
 *                   example: "ID123456789"
 *                 documents:
 *                   type: array
 *                   description: List of documents uploaded by the patient.
 *                   items:
 *                     type: string
 *                     example: "document1.pdf"
 *       400:
 *         description: Bad request, invalid or missing patient ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Patient ID is required"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while fetching patient details"
 */

patient.get("/getpatient", authorizationCheck, validateGetPatient, 
  async (req, res, next) => {
    try {
        return await getPatient(req, res);
    }
    catch (e) {
        next(e)
    }
} );

/**
 * @swagger
 * /patient/getpatientlatestappointment:
 *   get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Retrieve the latest appointment details of a patient
 *     description: Fetches the latest appointment details along with patient information for a specific patient by ID.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient to retrieve the latest appointment for.
 *         example: "60c72b2f5f1b2c001c8e4e09"
 *     responses:
 *       200:
 *         description: A successful response containing patient details and latest appointment information.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                   description: First name of the patient.
 *                   example: "John"
 *                 lastName:
 *                   type: string
 *                   description: Last name of the patient.
 *                   example: "Doe"
 *                 age:
 *                   type: integer
 *                   description: Age of the patient.
 *                   example: 30
 *                 birthday:
 *                   type: string
 *                   format: date
 *                   description: Birth date of the patient.
 *                   example: "1993-07-21"
 *                 gender:
 *                   type: string
 *                   description: Gender of the patient.
 *                   example: "Male"
 *                 mobile:
 *                   type: string
 *                   description: Mobile number of the patient.
 *                   example: "**********"
 *                 email:
 *                   type: string
 *                   description: Email address of the patient.
 *                   example: "<EMAIL>"
 *                 patientId:
 *                   type: string
 *                   description: Unique patient ID.
 *                   example: "P123456"
 *                 address:
 *                   type: string
 *                   description: Address of the patient.
 *                   example: "123 Main St, Cityville, 123456"
 *                 height:
 *                   type: number
 *                   description: Height of the patient in centimeters.
 *                   example: 175
 *                 weight:
 *                   type: number
 *                   description: Weight of the patient in kilograms.
 *                   example: 70
 *                 abhaAddress:
 *                   type: string
 *                   description: ABHA address of the patient.
 *                   example: "john.doe@abdm"
 *                 abhaNumber:
 *                   type: string
 *                   description: ABHA number of the patient.
 *                   example: "**********12"
 *                 documentType:
 *                   type: string
 *                   description: Type of document provided by the patient.
 *                   example: "ID Card"
 *                 documentNumber:
 *                   type: string
 *                   description: Document number provided by the patient.
 *                   example: "ID123456789"
 *                 documents:
 *                   type: array
 *                   description: List of documents uploaded by the patient.
 *                   items:
 *                     type: string
 *                     example: "document1.pdf"
 *                 appointment:
 *                   type: object
 *                   nullable: true
 *                   description: Details of the latest appointment (if available).
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: Unique ID of the appointment.
 *                       example: "60c72b2f5f1b2c001c8e4e10"
 *                     started:
 *                       type: string
 *                       format: date-time
 *                       description: Start time of the appointment.
 *                       example: "2024-09-15T10:00:00Z"
 *                     ended:
 *                       type: string
 *                       format: date-time
 *                       description: End time of the appointment.
 *                       example: "2024-09-15T11:00:00Z"
 *                     tokenNumber:
 *                       type: string
 *                       description: Token number for the appointment.
 *                       example: "T123"
 *                     timeSlot:
 *                       type: string
 *                       description: Time slot of the appointment.
 *                       example: "10:00 AM - 11:00 AM"
 *                     appointmentDate:
 *                       type: string
 *                       format: date-time
 *                       description: Date and time of the appointment.
 *                       example: "2024-09-15T10:00:00Z"
 *                     reason:
 *                       type: string
 *                       description: Reason for the appointment.
 *                       example: "General Checkup"
 *       400:
 *         description: Bad request, invalid or missing patient ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Patient ID is required"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while fetching patient details"
 */

patient.get(
  "/getpatientlatestappointment",
  authorizationCheck,
  validateGetPatientAppointmentOrMedical,
  async (req, res, next) => {
    try {
        return await getPatientLatestAppointment(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/getpatientandappointmentdetails:
 *   get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Retrieve patient details along with all appointments
 *     description: Fetches patient details and all their appointments by the patient's ID.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient to retrieve details and appointments for.
 *         example: "60c72b2f5f1b2c001c8e4e09"
 *     responses:
 *       200:
 *         description: A successful response containing patient details and all their appointments.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                   example: "John"
 *                 lastName:
 *                   type: string
 *                   example: "Doe"
 *                 age:
 *                   type: integer
 *                   example: 30
 *                 birthday:
 *                   type: string
 *                   format: date
 *                   example: "1993-07-21"
 *                 gender:
 *                   type: string
 *                   example: "Male"
 *                 mobile:
 *                   type: string
 *                   example: "**********"
 *                 email:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 patientId:
 *                   type: string
 *                   example: "P123456"
 *                 address:
 *                   type: object
 *                   properties:
 *                     house:
 *                       type: string
 *                       example: "123 Main St"
 *                     street:
 *                       type: string
 *                       example: "Main St"
 *                     city:
 *                       type: string
 *                       example: "Cityville"
 *                     pincode:
 *                       type: string
 *                       example: "123456"
 *                 height:
 *                   type: number
 *                   example: 175
 *                 weight:
 *                   type: number
 *                   example: 70
 *                 documentType:
 *                   type: string
 *                   example: "ID Card"
 *                 documentNumber:
 *                   type: string
 *                   example: "ID123456789"
 *                 documents:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "document1.pdf"
 *                 appointments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "60c72b2f5f1b2c001c8e4e10"
 *                       started:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-09-15T10:00:00Z"
 *                       ended:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-09-15T11:00:00Z"
 *                       tokenNumber:
 *                         type: string
 *                         example: "T123"
 *                       timeSlot:
 *                         type: string
 *                         example: "10:00 AM - 11:00 AM"
 *       500:
 *         description: Unexpected error during the retrieval of patient details and appointments.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Unexpected error"
 */

patient.get(
  "/getpatientandappointmentdetails",
  authorizationCheck,
  validateGetPatientAppointmentOrMedical,
  async (req, res, next) => {
    try {
        return await getPatientWithAllAppointment(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/getpatientandmedicalrecords:
 *   get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Retrieve patient details along with all medical records
 *     description: Fetches patient details and all their medical records by the patient's ID.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient to retrieve details and medical records for.
 *         example: "60c72b2f5f1b2c001c8e4e09"
 *     responses:
 *       200:
 *         description: A successful response containing patient details and all their medical records.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                   example: "John"
 *                 lastName:
 *                   type: string
 *                   example: "Doe"
 *                 age:
 *                   type: integer
 *                   example: 30
 *                 birthday:
 *                   type: string
 *                   format: date
 *                   example: "1993-07-21"
 *                 gender:
 *                   type: string
 *                   example: "Male"
 *                 mobile:
 *                   type: string
 *                   example: "**********"
 *                 email:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 patientId:
 *                   type: string
 *                   example: "P123456"
 *                 address:
 *                   type: object
 *                   properties:
 *                     house:
 *                       type: string
 *                       example: "123 Main St"
 *                     street:
 *                       type: string
 *                       example: "Main St"
 *                     city:
 *                       type: string
 *                       example: "Cityville"
 *                     pincode:
 *                       type: string
 *                       example: "123456"
 *                 height:
 *                   type: number
 *                   example: 175
 *                 weight:
 *                   type: number
 *                   example: 70
 *                 documentType:
 *                   type: string
 *                   example: "ID Card"
 *                 documentNumber:
 *                   type: string
 *                   example: "ID123456789"
 *                 documents:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "document1.pdf"
 *                 medicalRecords:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       recordId:
 *                         type: string
 *                         example: "60c72b2f5f1b2c001c8e4e11"
 *                       description:
 *                         type: string
 *                         example: "Annual check-up"
 *                       date:
 *                         type: string
 *                         format: date
 *                         example: "2023-08-15"
 *                       doctor:
 *                         type: string
 *                         example: "Dr. Smith"
 *       500:
 *         description: Unexpected error during the retrieval of patient details and medical records.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Unexpected error"
 */
patient.get(
  "/getpatientandmedicalrecords",
  authorizationCheck,
  validateGetPatientAppointmentOrMedical,
  async (req, res, next) => {
    try {
        return await getPatientWithAllMedical(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/addpatient:
 *   post:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Add patient details
 *     description: Adds a new patient based on the provided patient data.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               patientData:
 *                 type: object
 *                 required:
 *                   - patientId
 *                   - firstName
 *                   - lastName
 *                   - age
 *                   - height
 *                   - weight
 *                   - birthday
 *                   - gender
 *                   - mobile
 *                   - email
 *                   - address
 *                   - documentType
 *                   - documentNumber
 *                   - abhaAddress
 *                   - abhaNumber
 *                   - profilePic
 *                 properties:
 *                   patientId:
 *                     type: string
 *                     example: "P123456"
 *                   firstName:
 *                     type: string
 *                     example: "John"
 *                   lastName:
 *                     type: string
 *                     example: "Doe"
 *                   age:
 *                     type: integer
 *                     example: 30
 *                   height:
 *                     type: number
 *                     example: 175
 *                   weight:
 *                     type: number
 *                     example: 70
 *                   birthday:
 *                     type: string
 *                     format: date
 *                     example: "1993-07-21"
 *                   gender:
 *                     type: string
 *                     example: "Male"
 *                   mobile:
 *                     type: string
 *                     example: "**********"
 *                   email:
 *                     type: string
 *                     example: "<EMAIL>"
 *                   address:
 *                     type: object
 *                     properties:
 *                       house:
 *                         type: string
 *                         example: "123 Main St"
 *                       street:
 *                         type: string
 *                         example: "Main St"
 *                       city:
 *                         type: string
 *                         example: "Cityville"
 *                       pincode:
 *                         type: string
 *                         example: "123456"
 *                   documentType:
 *                     type: string
 *                     example: "ID Card"
 *                   documentNumber:
 *                     type: string
 *                     example: "ID123456789"
 *                   abhaAddress:
 *                     type: string
 *                     example: "abha@healthid"
 *                   abhaNumber:
 *                     type: string
 *                     example: "**********12"
 *                   profilePic:
 *                     type: string
 *                     example: "https://example.com/profile.jpg"
 *                   documents:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         fileName:
 *                           type: string
 *                           description: Name of the uploaded document.
 *                           example: "document1.pdf"
 *                         blobName:
 *                           type: string
 *                           description: Corresponding Azure Blob storage name.
 *                           example: "blob1.pdf"
 *     responses:
 *       200:
 *         description: A successful response indicating the patient has been added or updated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Unexpected error during adding or updating patient details.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error message"
 */

patient.post(
  "/addpatient",
  authorizationCheck,
  validateAddOrUpdatePatientDetails,
  async (req, res, next) => {
    try {
        return await addPatientDetails(req, res);
    }
    catch (e) {
        next(e)
    }
});


/**
 * @swagger
 * /patient/addpatientprofilewithabhadetails:
 *   post:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Add or update a patient profile with ABHA details
 *     description: Adds a new patient profile or updates an existing one with ABHA (Ayushman Bharat Health Account) details. If the patient already exists (based on firstName, mobile, and clinic), their profile is updated. Otherwise, a new patient profile is created.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - mobile
 *               - clientId
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: First name of the patient.
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 description: Last name of the patient.
 *                 example: "Doe"
 *               age:
 *                 type: number
 *                 description: Age of the patient.
 *                 example: 30
 *               height:
 *                 type: number
 *                 description: Height of the patient in centimeters.
 *                 example: 175
 *               weight:
 *                 type: number
 *                 description: Weight of the patient in kilograms.
 *                 example: 70
 *               birthday:
 *                 type: string
 *                 format: date
 *                 description: Birth date of the patient.
 *                 example: "1993-07-21"
 *               gender:
 *                 type: string
 *                 description: Gender of the patient.
 *                 example: "Male"
 *               mobile:
 *                 type: string
 *                 description: Mobile number of the patient.
 *                 example: "**********"
 *               email:
 *                 type: string
 *                 description: Email address of the patient.
 *                 example: "<EMAIL>"
 *               address:
 *                 type: string
 *                 description: Address of the patient.
 *                 example: "123 Main St, Cityville, 123456"
 *               abhaAddress:
 *                 type: string
 *                 description: ABHA address of the patient.
 *                 example: "john.doe@abdm"
 *               abhaNumber:
 *                 type: string
 *                 description: ABHA number of the patient.
 *                 example: "**********12"
 *               documentType:
 *                 type: string
 *                 description: Type of document provided by the patient.
 *                 example: "ID Card"
 *               documentNumber:
 *                 type: string
 *                 description: Document number provided by the patient.
 *                 example: "ID123456789"
 *               profilePic:
 *                 type: string
 *                 description: URL or path to the patient's profile picture.
 *                 example: "https://example.com/profile.jpg"
 *               documents:
 *                 type: array
 *                 description: List of documents uploaded by the patient.
 *                 items:
 *                   type: string
 *                   example: "document1.pdf"
 *               clientId:
 *                 type: string
 *                 description: ID of the clinic or client associated with the patient.
 *                 example: "60c72b2f5f1b2c001c8e4e09"
 *     responses:
 *       200:
 *         description: Patient profile added or updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   description: Details of the added or updated patient profile.
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "60c72b2f5f1b2c001c8e4e09"
 *                     firstName:
 *                       type: string
 *                       example: "John"
 *                     lastName:
 *                       type: string
 *                       example: "Doe"
 *                     age:
 *                       type: number
 *                       example: 30
 *                     height:
 *                       type: number
 *                       example: 175
 *                     weight:
 *                       type: number
 *                       example: 70
 *                     birthday:
 *                       type: string
 *                       format: date
 *                       example: "1993-07-21"
 *                     gender:
 *                       type: string
 *                       example: "Male"
 *                     mobile:
 *                       type: string
 *                       example: "**********"
 *                     email:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     address:
 *                       type: string
 *                       example: "123 Main St, Cityville, 123456"
 *                     abhaAddress:
 *                       type: string
 *                       example: "john.doe@abdm"
 *                     abhaNumber:
 *                       type: string
 *                       example: "**********12"
 *                     documentType:
 *                       type: string
 *                       example: "ID Card"
 *                     documentNumber:
 *                       type: string
 *                       example: "ID123456789"
 *                     profilePic:
 *                       type: string
 *                       example: "https://example.com/profile.jpg"
 *                     documents:
 *                       type: array
 *                       items:
 *                         type: string
 *                         example: "document1.pdf"
 *                     patientId:
 *                       type: string
 *                       example: "P123456"
 *                     clinic:
 *                       type: string
 *                       example: "60c72b2f5f1b2c001c8e4e09"
 *       400:
 *         description: Bad request, invalid or missing required fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "firstName, mobile, and clientId are required"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "An error occurred while processing the request"
 */

patient.post(
  "/addpatientprofilewithabhadetails",
  authorizationCheck,
   async (req, res, next) => {
    try {
        return await addPatientProfileWithAbhaDetails(req, res);
    }
    catch (e) {
        next(e)
    }
  }
)

/**
* @swagger
* /patient/updatepatient:
*   post:
*     tags:
*       - patient (mobile)
*       - patient
*     summary: Update existing patient details
*     description: Updates the details of an existing patient based on the provided patient data.
*     requestBody:
*       required: true
*       content:
*         application/json:
*           schema:
*             type: object
*             properties:
*               patientData:
*                 type: object
*                 required:
*                   - id
*                   - firstName
*                   - lastName
*                   - age
*                   - height
*                   - weight
*                   - birthday
*                   - gender
*                   - mobile
*                   - email
*                 properties:
*                   id:
*                     type: string
*                     example: "609e1f3b8b75b8b4a9f9d4b1"
*                   firstName:
*                     type: string
*                     example: "John"
*                   lastName:
*                     type: string
*                     example: "Doe"
*                   age:
*                     type: integer
*                     example: 30
*                   height:
*                     type: number
*                     example: 175
*                   weight:
*                     type: number
*                     example: 70
*                   birthday:
*                     type: string
*                     format: date
*                     example: "1993-07-21"
*                   gender:
*                     type: string
*                     example: "Male"
*                   mobile:
*                     type: string
*                     example: "**********"
*                   email:
*                     type: string
*                     example: "<EMAIL>"
*                   address:
*                     type: object
*                     properties:
*                       house:
*                         type: string
*                         example: "123 Main St"
*                       street:
*                         type: string
*                         example: "Main St"
*                       city:
*                         type: string
*                         example: "Cityville"
*                       pincode:
*                         type: string
*                         example: "123456"
*                   documentType:
*                     type: string
*                     example: "ID Card"
*                   documentNumber:
*                     type: string
*                     example: "ID123456789"
*                   abhaAddress:
*                     type: string
*                     example: "ABHA12345"
*                   abhaNumber:
*                     type: string
*                     example: "9876543210"
*                   profilePic:
*                     type: string
*                     example: "https://example.com/profile.jpg"
*                   documents:
*                     type: array
*                     items:
*                       type: object
*                       properties:
*                         fileName:
*                           type: string
*                           description: Name of the uploaded document.
*                           example: "document1.pdf"
*                         blobName:
*                           type: string
*                           description: Corresponding Azure Blob storage name.
*                           example: "blob1.pdf"
*                   clinic:
*                     type: string
*                     example: "662ca0a41a2431e16c41ebaa"
*     responses:
*       200:
*         description: A successful response indicating the patient has been updated.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: true
*       500:
*         description: Unexpected error during updating patient details.
*         content:
*           application/json:
*             schema:
*               type: object
*               properties:
*                 success:
*                   type: boolean
*                   example: false
*                 error:
*                   type: string
*                   example: "Error message"
*/

patient.post(
  "/updatepatient",
  authorizationCheck,
  validateAddOrUpdatePatientDetails,
  async (req, res, next) => {
    try {
        return await updatePatientDetails(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/searchmobile:
 *    get:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Search for a patient by mobile number
 *     description: Retrieves patient details based on the provided mobile number and clinic ID.
 *     parameters:
 *       - in: query
 *         name: mobile
 *         required: true
 *         schema:
 *           type: string
 *           example: "**********"
 *         description: The mobile number of the patient to search for.
 *       - in: query
 *         name: clinicId
 *         required: true
 *         schema:
 *           type: string
 *           example: "609e1f3b8b75b8b4a9f9d4b1"
 *         description: The ID of the clinic to filter the search.
 *       - in: query
 *         name: size
 *         required: false
 *         schema:
 *           type: integer
 *           example: 10
 *         description: The number of results to return (optional).
 *     responses:
 *       200:
 *         description: A successful response containing patient data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "609e1f3b8b75b8b4a9f9d4b1"
 *                       firstName:
 *                         type: string
 *                         example: "John"
 *                       lastName:
 *                         type: string
 *                         example: "Doe"
 *                       mobile:
 *                         type: string
 *                         example: "**********"
 *                       email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       address:
 *                         type: object
 *                         properties:
 *                           city:
 *                             type: string
 *                             example: "Cityville"
 *       500:
 *         description: Unexpected error during the search operation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error message"
 */
patient.get(
  "/searchmobile",
  authorizationCheck,
  validateSearchPatientMobile,
  async (req, res, next) => {
    try {
        return await searchPatientMobile(req, res);
    }
    catch (e) {
        next(e)
    }
});

/**
 * @swagger
 * /patient/deletepatient:
 *   delete:
 *     tags:
 *       - patient (mobile)
 *       - patient
 *     summary: Delete a patient record
 *     description: Removes a patient record based on the provided patient ID and sends a notification.
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           example: "609e1f3b8b75b8b4a9f9d4b1"
 *         description: The ID of the patient to be deleted.
 *       - in: query
 *         name: Notificationkey
 *         required: true
 *         schema:
 *           type: string
 *           example: "your_notification_key"
 *         description: The notification key to send notifications.
 *       - in: query
 *         name: user
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *               example: "609e1f3b8b75b8b4a9f9d4b2"
 *             name:
 *               type: string
 *               example: "Admin User"
 *         description: The user performing the deletion action.
 *     responses:
 *       200:
 *         description: A successful response indicating that the patient has been deleted.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Unexpected error during the deletion operation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   example: "Error message"
 */
patient.delete(
  "/deletepatient",
  authorizationCheck,
  validateDeletePatient,
  async (req, res, next) => {
    try {
        return await deletePatient(req, res);
    }
    catch (e) {
        next(e)
    }
}
  
);

export default patient;
